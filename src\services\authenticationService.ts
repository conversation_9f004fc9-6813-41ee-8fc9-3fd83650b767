import curlHelper from '../helpers/curlHelper'
import config from '../config/appConfig'

export default class Authenticationservice {

  private baseUrl = config.services.authentication.baseUrl;

  public fetchAuthUsers = async (userId: string, token: string): Promise<object> => {
    try {
      const endpoint: string = `${this.baseUrl}/user/${userId}`
      const authorization: string = token ? token : ""
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": authorization,
          "userid": userId
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('get', endpoint, options, {});
      if (!serviceResponseObject.isError) {
        return { status: true, message: "retrived successfully", data: serviceResponseObject.body.data }
      }
      console.log(serviceResponseObject)
      return { status: false, message: "failed to fetch details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("fetchAuthUsers failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }
 

  
  

  public createwebuser = async (payload: object): Promise<object> => {
    try {

      const endpoint: string = `${this.baseUrl}/webusers/register`
      console.log("auth endpoint ::", endpoint);
      let options: object = {
        headers: {
          "content-type": "application/json"
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, payload);
      if (!serviceResponseObject.isError) {
        return { status: true, message: "verified successfully", data: serviceResponseObject.body.data }
      }
      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to verify auth user details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("fetchAuthUsers failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }

  public verifyWebUser = async (payload: object): Promise<object> => {
    try {
      const endpoint: string = `${this.baseUrl}/webusers/verify`
      console.log("auth endpoint ::", endpoint);
      let options: object = {
        headers: {
          "content-type": "application/json"
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, payload);
      if (!serviceResponseObject.isError) {
        return { status: true, message: "verified successfully", data: serviceResponseObject.body.data }
      }
      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to verify auth user details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("fetchAuthUsers failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }

  

 

  public sendMail = async (req: any) => {
    try {
      const endpoint: string = `${this.baseUrl}/google/gmail`
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": req.headers.authorization ? req.headers.authorization : null,
          "userid": req.headers.userid ? req.headers.userid : null
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, req.body);
      if (!serviceResponseObject.isError) {
        return { status: true, message: "Mail sent sucessfully", data: serviceResponseObject.body.data }
      }
      return { status: false, message: "Failed to sent mail", data: serviceResponseObject.body };
    } catch (error: any) {
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }

  public getMail = async (req: any, id: string) => {
    try {
      const endpoint: string = `${this.baseUrl}/google/gmail/${id}`
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": req.headers.authorization ? req.headers.authorization : null,
          "userid": req.headers.userid ? req.headers.userid : null,
          "access_token": req.headers.access_token ? req.headers.access_token : null
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('delete', endpoint, options, {});
      if (!serviceResponseObject.isError) {
        return { status: true, message: "Sucessfully found", data: serviceResponseObject.body.data }
      }
      return { status: false, message: "Failed to find mail", data: serviceResponseObject.body };
    } catch (error: any) {
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }
}
