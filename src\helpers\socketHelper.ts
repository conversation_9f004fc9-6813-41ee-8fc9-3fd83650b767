import { Server } from 'socket.io';
import * as http from 'http';
import * as express from 'express';
import messageDal from '../dal/messageDal';

class SocketHelper {
  public app: express.Application;
  public io: Server;
  private httpServer: http.Server;
  public userSocketMap: any;
  private static instance: SocketHelper;
  private messageDal: messageDal;
  private isSetupComplete: boolean = false; // Add a flag to check if setup is complete

  private constructor(messageDal: messageDal) {
    this.app = express();
    this.httpServer = http.createServer(this.app);
    this.userSocketMap = {}; // {userId: socketId}
    this.io = new Server(this.httpServer, {
      cors: {
        origin: '*', // Update as needed for security
        methods: ['GET', 'POST'],
      },
      path: '/chat/socket.io', // Custom socket path
    });
    this.messageDal = messageDal;
  }

  public static getInstance(messageDal: any | null): SocketHelper {
    if (!SocketHelper.instance) {
      SocketHelper.instance = new SocketHelper(messageDal);
    }
    return SocketHelper.instance;
  }

  public setMessageDal(messageDal: messageDal) {
    this.messageDal = messageDal;
  }

  public theApp = () => {
    return this.app;
  }

  public theHttpServer = () => {
    return this.httpServer;
  }

  public getReceiverSocketId = (senderId: any) => {
    return this.userSocketMap[senderId];
  };

  public getUserIdBySocketId(socketId: string): string | null {
    for (const [userId, id] of Object.entries(this.userSocketMap)) {
      if (id === socketId) {
        return userId;
      }
    }
    return null;
  }

  public socketSetup() {
    if (this.isSetupComplete) return this.io; // Return if setup is already complete

    const io = this.io;

    io.on('connection', (socket) => {
      const userId: any = socket.handshake.query.userId;
    
      if (!userId) {
        socket.emit('error', 'userId is required');
        return;
      }
      console.log(`user connected with id: ${userId} and socket id: ${socket.id}`);
      global.logger.info(`user connected with id: ${userId} and socket id: ${socket.id}`);
      if (userId != 'undefined') this.userSocketMap[userId] = socket.id;
      io.emit('getOnlineUsers', Object.keys(this.userSocketMap));
      io.emit('onlineStatus', { userId: userId, status: true });

      socket.on('joinGroup', async (data) => {
        if (!data.subOrderNumber && !userId) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }

        console.log('an user joined by id', userId, " with data :", data);

        socket.join(data.subOrderNumber);

        let lastOnline = await this.messageDal.updateMessageForMember(userId, data.subOrderNumber, data.type);
        if (!lastOnline.status) {
          socket.emit('error', 'error is occured while updating last online');
          return;
        }
        socket.to(data.subOrderNumber).emit('userJoined', { message: `${data.userId} has joined the group.` });
      });

      socket.on('leaveGroup', async (data) => {
        if (!data.subOrderNumber) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }
        let lastOnline = await this.messageDal.updateMessageForMember(userId, data.subOrderNumber, data.type);
        if (!lastOnline.status) {
          socket.emit('error', 'error is occured while updating last online');
          return;
        }
        socket.leave(data.subOrderNumber);
      });

      socket.on('getPendingMessages', async (data) => {
        const { userId, subOrderNumber, type } = data;
        const pending = await this.messageDal.getPendingMessages(userId, subOrderNumber, type);

        const receiverSocketId = this.getReceiverSocketId(userId);
        if (receiverSocketId) {
          io.to(receiverSocketId).emit('pendingMessagesUpdated', { subOrderNumber, pendingMessages: pending.data });
        }
      });

      socket.on('displayChat', async (data) => {
        if (!data.subOrderNumber) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }

        if (!data.type) {
          socket.emit('error', 'type is required');
          return;
        }

        let result;

        if (data.type === 'user') {
          const sellerId: any = await this.messageDal.findSellerId(data.subOrderNumber, userId);
          const userData: any = await this.messageDal.findUser(sellerId);
          let active = Object.keys(this.userSocketMap).includes(sellerId);
          result = {
            sellerId: sellerId,
            userId: userId,
            userData,
            subOrderNumber: data.subOrderNumber,
            onlineStatus: active,
          };
        } else if (data.type === 'seller') {
          const userid: any = await this.messageDal.findUserId(data.subOrderNumber, userId);
          const userData: any = await this.messageDal.findUser(userid);
          let active = Object.keys(this.userSocketMap).includes(userid);
          result = {
            sellerId: userId,
            userId: userid,
            userData,
            subOrderNumber: data.subOrderNumber,
            onlineStatus: active,
          };
        } else {
          const usersData: any = await this.messageDal.getSellerAndUserId(data.subOrderNumber);
          result = {
            sellerId: usersData.userId,
            userId: usersData.sellerId,
            subOrderNumber: data.subOrderNumber,
          };
        }

        socket.emit('chat', result);
      });

      socket.on('allCount', async (data) => {
        if (!data.type) {
          socket.emit('error', 'type is required');
          return;
        }

        const count: any = await this.messageDal.getPendingMessagesForUserOrSeller(userId, data.type);

        socket.emit('chatCount', count);
      });

      socket.on('readMessages', async (data) => {
        const req = {
          params: { id: data.subOrderNumber },
          query: { limit: data.limit, offset: data.offset, userId: userId },
        };
        const updations = await this.messageDal.getConversation(req);
        socket.emit('readMessages', updations.data);
      });

      socket.on('readNotification', async (data) => {
        const req = {
          params: { id: userId },
          query: { limit: data.limit, offset: data.offset },
        };
        const result = await this.messageDal.getNotificationsByUserId(req);
        socket.emit('readNotification', result.data);
      });

      socket.on('allNotification', async (data) => {
        const req = {
          params: { id: userId },
        };
        const result = await this.messageDal.getUnreadNotificationsCount(req);
        socket.emit('notificationCount', result.data);
      });

      socket.on('moveNotification', async (data) => {
        const req = {
          params: { userid: userId },
        };
        const result = await this.messageDal.updateUserOnline(req);
        socket.emit('moveNotification', result.data);
      });

      socket.on('getMembers', async (data) => {
        const req = {
          params: { id: userId },
          query: {
            limit: data?.limit,
            offset: data?.offset,
            type: data.type,
            orderNumber: data?.orderNumber,
            search: data?.search,
          },
          headers: {
            userid: socket.handshake.query.userId,
            authorization: socket.handshake.query.token,
          },
        };

        let members: any;
        if (data.type === 'user') {
          members = await this.messageDal.getMembersforUser(req);
          if (!members.status) {
            socket.emit('error', 'error is occured while getting members');
            return;
          }
        }
        if (data.type === 'seller') {
          members = await this.messageDal.getMembersforSeller(req);
          if (!members.status) {
            socket.emit('error', 'error is occured while getting members');
            return;
          }
        }
        if (data.type === 'admin') {
          members = await this.messageDal.adminOrderMember(req);
          if (!members.status) {
            socket.emit('error', 'error is occured while getting members');
            return;
          }
        }
        socket.emit('membersData', members.data);
      });

      socket.on('disconnect', () => {
        console.log('user disconnected with userid : ',userId," from socketId : ", socket.id);
        delete this.userSocketMap[userId];
        io.emit('getOnlineUsers', Object.keys(this.userSocketMap));
        io.emit('onlineStatus', { userId: userId, status: false });
      });
    });

    this.isSetupComplete = true; // Mark setup as complete
    return io;
  }
}

export default SocketHelper;