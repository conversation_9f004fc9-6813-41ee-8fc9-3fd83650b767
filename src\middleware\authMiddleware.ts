import { Request, Response, NextFunction } from 'express'
import AuthService from '../services/authenticationService';
import * as jwt from 'jsonwebtoken';

const authService = new AuthService()

const AuthMiddleWare = async (req: Request, res: Response, next: NextFunction) => {
  try {
    global.logger.silly(`Method: ${req.method}, Path: ${req.originalUrl}`)

    if (!req.headers.userid || (req.headers.userid == "")) {
      return res.status(401).send({ status: false, message: 'Invalid Request', data: 'Please Check userid' })
    }
    const authorization: string = req.headers.authorization ? req.headers.authorization : ''
    if (!authorization) {
      return res.status(401).send({ status: false, message: 'token missing', data: 'Token missing in headers' })
    }
    const tokenDecoded: any = jwt.decode(authorization)
    if (!tokenDecoded) {
      return res.status(401).send({ status: false, message: 'Authentication error-Token Decoding Failed', data: tokenDecoded })
    }
    const tokenUserId = tokenDecoded.userId;
    if (!tokenUserId) {
      return res.status(401).send({ status: false, message: 'Authentication error-Token Missing User Data', data: "User data missing" })
    }
    let userId: any = req.headers.userid;
    if (userId != tokenUserId) {
      return res.status(401).send({ status: false, message: 'Invalid user', data: "Invalid user access" })
    }
   
    let result : any= await authService.fetchAuthUsers(userId,authorization)
    if (!result.status) {
      return res.status(401).send({ status: false, message: 'Invalid user', data: 'User not found' })
    }
    if (!result.data.secret) {
      return res.status(401).send({ status: false, message: 'Authentication error-jwt secret not found', data: 'secret not found' })
    }
    let verify: any = jwt.verify(authorization, result.data.secret)
    if (!verify) {
      return res.status(403).send({ code: 403, status: false, message: "unauthorized", data: verify });
    }
    next()
  } catch (error: any) {
    console.log("error", error)
    return res.status(500).send({code:500, status: false, message: 'Internal-Server-Error', data: error.message ? error.message : "Internal server error" })
  }
}

export default AuthMiddleWare
