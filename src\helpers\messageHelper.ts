import { connection , Types} from "mongoose";

export default class messageHelper {

    // Function to find userId given subOrderNumber and sellerId

  

    public search = async (reqQuery: any) => {
      try {
        let searchArr: any = [{}]
        if (reqQuery?.search) {
          let search = reqQuery.search
          searchArr = ([
            { 'subOrderNumber': { $regex: search, $options: 'i' } },
            { 'user.userName': { $regex: search, $options: 'i' } },
            { 'user.displayName': { $regex: search, $options: 'i' } },
          ])
        }
        return searchArr
      }
      catch (err: any) {
        return { status: false, code: 409, message: 'Something went wrong', data: err.message }
      }
    }

}