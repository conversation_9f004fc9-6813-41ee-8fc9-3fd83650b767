import { Request, Response, NextFunction } from 'express'
import * as jwt from 'jsonwebtoken'
import ApiResponse from '../helpers/response'

interface IReqResDefault extends Request {
  authRequired: boolean
}
const authErrorMessages = ['invalid signature', 'jwt expired']
const Authentication = async (req: IReqResDefault, resp: Response, next: NextFunction) => {
  try {
    global.logger.debug(`Authentication Required ? ${req.authRequired}`)
    const authorization = req.headers.authorization ? req.headers.authorization : ''
    const token = authorization.split(' ')[1]
    const tokenDecoded: any = jwt.decode(token)
    if (!tokenDecoded) {
      return new ApiResponse(403, false, 'Authentication error-Token Decoding Failed', tokenDecoded)
    }
    next()
  } catch (error: any) {
    if (authErrorMessages.includes(error.message)) {
      next(new ApiResponse(403, false, error.message))
    } else {
      next(error)
    }
  }
}

export default Authentication
