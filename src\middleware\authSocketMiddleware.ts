import { Socket } from 'socket.io';
import * as jwt from 'jsonwebtoken';
import AuthService from '../services/authenticationService';

const authService = new AuthService();

const authSocketMiddleware = async (socket: Socket, next: (err?: Error) => void) => {
  try {
    
    const token : any = socket.handshake.query.token || socket.handshake.headers['authorization'];
    if (!token) {
      return next(new Error('Authentication error: Token missing'));
    }
    const userId : any = socket.handshake.headers['userid'];
    if (!userId) {
      return next(new Error('Authentication error: userid missing'));
    }
    const tokenDecoded: any = jwt.decode(token)
    if (!tokenDecoded) {
      return next(new Error( 'Authentication error-Token Decoding Failed'))
    }
    const tokenUserId = tokenDecoded.userId;
    if (!tokenUserId) {
      return next(new Error('Authentication error-Token Missing User Data'))
    }

    const userData : any = await authService.fetchAuthUsers(tokenUserId, token);
    if (!userData.status) {
      return next(new Error('Authentication error: Invalid user'));
    }
    if (!userData.data.secret) {
      return next(new Error('Authentication error-jwt secret not found'));
    }
    let verify: any = jwt.verify(token,userData.data.secret)
    if (!verify) {
      return next(new Error("unauthorized"));
    }
    socket.data.userId = userId; // Store userId in socket data for later use
    next();
  } catch (error : any ) {
    next(new Error('Authentication error: ' + error.message));
  }
};

export default authSocketMiddleware;