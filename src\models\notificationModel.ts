import * as mongoose from 'mongoose';
import { INotificationModel } from './Interfaces/notificationInterface';



const notificationSchema = new mongoose.Schema({
    userid: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
    },
    role: {
        type: String,
        required: true
    },
    type: {
        type: String,
        required: true
    },
    content: {
        type: String,

    },
    // isRead: { 
    //     type: Boolean,
    //     default: false
    //   }
},
    { timestamps: true });


export const notificationModel: mongoose.Model<INotificationModel> = mongoose.model<INotificationModel>('notification', notificationSchema)
