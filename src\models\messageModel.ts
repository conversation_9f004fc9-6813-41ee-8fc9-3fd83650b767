import { Schema, Model, model } from 'mongoose';
import { IMessageModel } from './Interfaces/messageInterface';

const imageSchema = new Schema({
    image: { type: String },
    type: { type: String },
    fileName: { type: String },
    // createdAt: { type: Date, default: Date.now }
})

const messageSchema = new Schema({
    // Enhanced fields
    chatType: { type: String, enum: ['batayeq', 'fitness'], default: 'batayeq' },

    // Existing fields
    sender: { type: Schema.Types.ObjectId , required: true},
    message: { type: String },
    image: {
        image: { type: String },
        type: { type: String },
        fileName: { type: String },
    },
    createdAt: { type: Date, default: Date.now },

    // Conditional fields based on chatType
    // For Batayeq
    subOrderNumber: { type: String }, // Only for batayeq
    role: { type: String }, // Only for batayeq

    // For Fitness
    receiverId: { type: Schema.Types.ObjectId }, // Only for fitness
    conversationId: { type: String }, // Generated from [senderId, receiverId].sort().join('_')
})

// Existing indexes
messageSchema.index({ subOrderNumber: 1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ createdAt: -1 });

// New indexes for fitness chat
messageSchema.index({ chatType: 1 });
messageSchema.index({ conversationId: 1 });
messageSchema.index({ receiverId: 1 });


export const messageModel: Model<IMessageModel> = model<IMessageModel>('message', messageSchema)