import { Schema, Model, model } from 'mongoose';
import { IMessageModel } from './Interfaces/messageInterface';

const imageSchema = new Schema({
    image: { type: String },
    type: { type: String },
    fileName: { type: String },
    // createdAt: { type: Date, default: Date.now }
})

const messageSchema = new Schema({
    subOrderNumber: { type: String, required: true },
    sender: { type: Schema.Types.ObjectId , required: true},
    message: { type: String },
    image: {
        image: { type: String },
        type: { type: String },
        fileName: { type: String },
    },
    role: { type: String },
    createdAt: { type: Date, default: Date.now }
})

messageSchema.index({ subOrderNumber: 1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ createdAt: -1 });


export const messageModel: Model<IMessageModel> = model<IMessageModel>('message', messageSchema)