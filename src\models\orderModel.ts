import * as mongoose from 'mongoose';
import { add } from 'winston';

const addressSchema = new mongoose.Schema({
    firstName: { type: String, },
    middleName: { type: String },
    lastName: { type: String, },
    phone: {
        countryCode: { type: String, },
        number: { type: String, }
    },
    addressLine1: { type: String, },
    addressLine2: { type: String },
    city: { type: String, },
    area: { type: String, },
    country: { type: String, },
    landmark: { type: String },
});


const  cardSchema = new mongoose.Schema({ 

    inventoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Inventory' ,required : true},
    quantity: { type: Number ,required: true },
    price: { type: Number  ,required: true},
    status: { type: String,enum :["active", "canceled", "returned","completed"], default: 'active' },
    cancellationReason :{type: String},
    CancellationDate:{type: Date}
 })


const sellerSchema = new mongoose.Schema({
    cardData: [cardSchema],
    sellerId  : { type: mongoose.Schema.Types.ObjectId, ref: 'users' ,required: true },
    subOrderNumber: { type: String, required: true },
    deliveryCharge: { type: Number, required: true },
    arrivalDate: { type: Date  },
    shippingAddress: addressSchema,
    billingAddress: addressSchema,
    sellerOnline:{type: Date},
    userOnline:{type: Date},
    // deliveryDays: { type: Number ,required: true },
    status: { type: String,enum :["placed", "packaging","delivered"], default: 'placed' },
});


const orderSchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'users' ,required: true},
    sellerData: [sellerSchema],
    orderNumber: { type: String, required: true },
    confirmedDate: { type: Date },
    createdById: { type: mongoose.Schema.Types.ObjectId, ref: 'users' },
    updatedById: { type: mongoose.Schema.Types.ObjectId, ref: 'users'}
},
    { timestamps: true });


export const orderModel = mongoose.model('order', orderSchema)
