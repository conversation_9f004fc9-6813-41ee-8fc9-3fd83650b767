export default class ApiResponse {
  code: number;
  status: boolean;
  message: string | undefined;
  data: any;
  error: string | undefined;
  constructor(code: number = 200, status: boolean = true, message: string | undefined = undefined, data: any = undefined, error: string | undefined = undefined) {
    this.code = code;
    this.status = status;
    this.message = message;
    this.data = data;
    this.error = error;
  }
}