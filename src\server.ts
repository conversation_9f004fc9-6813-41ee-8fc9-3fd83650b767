import App from './helpers/appServer';
import config from './config/appConfig';
import * as bodyParser from 'body-parser';
import cors = require('cors');
import DefaultRoute from './routes/defaultRoute';
import messageRoute from './routes/messageRoute';
import dbConnection from './db/connection';
import * as cluster from 'cluster';
import AuthMiddleWare from './middleware/authMiddleware';
import SocketHelper from './helpers/socketHelper';
// import messageDal from './dal/messageDal';

const bootServer = async () => {
  await dbConnection(); // Ensure this is called only once per worker
  const socketHelper = SocketHelper.getInstance(null); // Temporarily pass null
  socketHelper.socketSetup(); // Ensure socket setup is called only once

  const app = new App({
    port: config.app.port,
    defaults: [cors()],
    middleWares: [
      bodyParser.json(),
      bodyParser.urlencoded({ extended: true }),
      // AuthMiddleWare,
    ],
    routes: [
      new DefaultRoute(),
      new messageRoute(socketHelper) // Pass the socketHelper instance
    ],
    socketHelper: socketHelper // Pass the socketHelper instance
  });
  app.listen();
};

if (cluster.isMaster) {
  let a = 1;
  console.log('server is running through cluster');
  for (let i = 0; i < a; i++) {
    cluster.fork();
  }
  cluster.on('exit', worker => {
    console.log(`worker ${worker.process.pid} died`);
  });
} else {
  bootServer();
}