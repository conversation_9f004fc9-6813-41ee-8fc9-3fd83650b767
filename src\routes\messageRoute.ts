import * as express from 'express';
import Messages from '../controller/messageController';
import ApiResponse from '../helpers/response';
import * as multer from 'multer';
import SocketHelper from '../helpers/socketHelper';

const storage = multer.memoryStorage(); // Use memory storage to get the file buffer directly
const upload = multer({ storage: storage, limits: { fileSize: 5242880 } });

class messageRoute {
  public path = '/';
  public router = express.Router();
  private messageController: Messages;

  constructor(socketHelper: SocketHelper) {
    this.messageController = new Messages(socketHelper); // Pass the socketHelper instance
    this.initRoutes();
  }

  private fileValidation(req: any, res: any, next: any) {
    upload.array('files')(req, res, (err) => {
        if (err) {
            console.log('file err', err);
            if (err instanceof multer.MulterError) {
                return res.status(400).json({code:400,status:false, message: `Multer error: ${err.message}`,data:[] });
            }
            return res.status(500).json({ error: 'An unknown error occurred.' });
        }
        next();
    });
  }

  public initRoutes() {
    this.router.route('/message/:id')
      .post(this.fileValidation, async (req, res) => {
        const resBody: ApiResponse = await this.messageController.createMessage(req);
        return res.status(resBody.code).send(resBody);
      });

    this.router.route('/notification/:userid')
      .post(this.fileValidation, async (req, res) => {
        const resBody: ApiResponse = await this.messageController.sendNotification(req);
        return res.status(resBody.code).send(resBody);
      });

    this.router.route('/member/:id')
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.messageController.getMembers(req);
        return res.status(resBody.code).send(resBody);
      });

    this.router.route('/message/:id')
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.messageController.getConversation(req);
        return res.status(resBody.code).send(resBody);
      });
  }
}

export default messageRoute;