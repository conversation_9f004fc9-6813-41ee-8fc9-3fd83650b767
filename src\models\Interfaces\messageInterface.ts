import { Document, Types } from 'mongoose'
export interface IMessageModel {
    _id: Types.ObjectId
    subOrderNumber?: string // Optional for fitness messages
    sender: Types.ObjectId
    message: string
    image : string
    createdAt: Date
    role?: string // Optional for fitness messages
    chatType: 'batayeq' | 'fitness' // New field to distinguish message types
    receiverId?: Types.ObjectId // Only for fitness messages
    conversationId?: string // Only for fitness messages
}