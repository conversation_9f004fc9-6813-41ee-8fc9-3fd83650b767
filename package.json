{"name": "appointment-microservice", "version": "1.0.0", "description": "", "main": "dist/src/server.js", "types": "dist/src/server.d.ts", "scripts": {"build": "tsc", "start": "npm run build && node dist/src/server.js", "serve": "node dist/src/server.js", "auto": "node --inspect=5858 -r ts-node/register ./src/server.ts", "auto:watch": "nodemon"}, "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/socket.io": "^3.0.2", "@aws-sdk/client-s3": "^3.735.0", "@aws-sdk/lib-storage": "^3.685.0", "@aws-sdk/s3-request-presigner": "^3.685.0", "app-root-path": "^3.0.0", "axios": "^1.7.7", "body-parser": "^1.20.3", "cluster": "^0.7.7", "cors": "^2.8.5", "express": "^4.21.0", "form-data": "^4.0.1", "jsonwebtoken": "^8.5.1", "mongoose": "^7.6.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "winston": "^3.3.3"}, "devDependencies": {"@types/app-root-path": "^1.2.4", "@types/jsonwebtoken": "^8.5.0", "@types/multer": "^1.4.12", "@types/node": "14.18.3", "typescript": "^4.9.5"}}