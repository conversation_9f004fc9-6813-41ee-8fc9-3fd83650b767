# Fitness Chat Integration Plan

## Strategy Overview
Add fitness chat functionality to the existing Batayeq socket system using a **dual-mode approach** that preserves all existing Batayeq functionality while adding fitness-specific features.

## Implementation Plan

### Phase 1: Enhance Socket Event Structure

#### 1.1 Add Chat Type Detection
```javascript
// Add to existing socket events
socket.on('connection', (socket) => {
  const userId = socket.handshake.query.userId;
  const chatType = socket.handshake.query.chatType || 'batayeq'; // Default to batayeq
  
  // Store chat type in socket for context
  socket.chatType = chatType;
  
  // Existing Batayeq logic remains unchanged
  // ...existing code...
});
```

#### 1.2 Modify Event Handlers with Conditional Logic
Instead of creating new events, enhance existing ones with type checking:

```javascript
// Example: Enhanced readMessages event
socket.on('readMessages', async (data) => {
  if (socket.chatType === 'fitness') {
    // Fitness: Direct chat between two users
    const req = {
      senderId: userId,
      receiverId: data.receiverId,
      limit: data.limit,
      offset: data.offset
    };
    const messages = await this.messageDal.getFitnessConversation(req);
    socket.emit('readMessages', messages.data);
  } else {
    // Existing Batayeq logic (unchanged)
    const req = {
      params: { id: data.subOrderNumber },
      query: { limit: data.limit, offset: data.offset, userId: userId },
    };
    const updations = await this.messageDal.getConversation(req);
    socket.emit('readMessages', updations.data);
  }
});
```

### Phase 2: Enhance MessageDal Layer

#### 2.1 Add Fitness-Specific Methods
```javascript
// Add to messageDal class
class MessageDal {
  // Existing Batayeq methods remain unchanged
  
  // New fitness methods
  async getFitnessConversation(req) {
    // Get direct messages between two users
  }
  
  async getFitnessMembers(userId) {
    // Get all users who have chatted with this user
  }
  
  async updateFitnessLastSeen(userId) {
    // Update last seen in user collection
  }
  
  async sendFitnessMessage(senderId, receiverId, message) {
    // Send direct message between users
  }
}
```

#### 2.2 Message Sending Strategy
```javascript
socket.on('sendMessage', async (data) => {
  if (socket.chatType === 'fitness') {
    // Fitness: Send to specific user's socket
    const message = await this.messageDal.sendFitnessMessage(
      userId, 
      data.receiverId, 
      data.message
    );
    
    const receiverSocketId = this.getReceiverSocketId(data.receiverId);
    if (receiverSocketId) {
      io.to(receiverSocketId).emit('newMessage', message);
    }
    socket.emit('messageSent', message);
    
  } else {
    // Existing Batayeq group message logic
    // Send to room (subOrderNumber)
    socket.to(data.subOrderNumber).emit('newMessage', data);
  }
});
```

### Phase 3: Database Schema Considerations

#### 3.1 Message Collection Enhancement
```javascript
// Add chatType field to distinguish message types
const messageSchema = {
  // Existing fields
  senderId: String,
  message: String,
  timestamp: Date,
  
  // Enhanced fields
  chatType: { type: String, enum: ['batayeq', 'fitness'], default: 'batayeq' },
  
  // Conditional fields based on chatType
  // For Batayeq
  subOrderNumber: String, // Only for batayeq
  
  // For Fitness
  receiverId: String, // Only for fitness
  conversationId: String, // Generated from [senderId, receiverId].sort().join('_')
};
```


### Phase 4: Event Mapping Strategy

#### 4.1 Conditional Event Handling
| Event | Batayeq Behavior | Fitness Behavior |
|-------|------------------|------------------|
| `joinGroup` | Join room by subOrderNumber | Join personal room by userId |
| `leaveGroup` | Leave room, update order lastSeen | Update user lastSeen |
| `getMembers` | Get order participants | Get chat contacts |
| `displayChat` | Get order chat info | Get direct chat info |
| `getPendingMessages` | Get unread by subOrderNumber | Get unread by senderId |

#### 4.2 Room Management
```javascript
// Fitness users join personal rooms for direct messaging
socket.on('joinGroup', async (data) => {
  if (socket.chatType === 'fitness') {
    // Join personal room and conversation rooms
    socket.join(`user_${userId}`);
    if (data.conversationId) {
      socket.join(data.conversationId);
    }
    
    // Update fitness last seen
    await this.messageDal.updateFitnessLastSeen(userId);
    
  } else {
    // Existing Batayeq logic (unchanged)
    socket.join(data.subOrderNumber);
    let lastOnline = await this.messageDal.updateMessageForMember(
      userId, data.subOrderNumber, data.type
    );
    // ...existing code...
  }
});
```

### Phase 5: Client-Side Integration

#### 5.1 Connection Setup
```javascript
// Client connects with chatType parameter
const socket = io('/chat', {
  query: {
    userId: currentUserId,
    chatType: 'fitness' // or 'batayeq'
  }
});
```

#### 5.2 Event Handling
```javascript
// Same events, different data structures
socket.emit('readMessages', {
  // For fitness
  receiverId: targetUserId,
  limit: 20,
  offset: 0
  
  // For batayeq (existing)
  // subOrderNumber: orderNumber,
  // limit: 20,
  // offset: 0
});
```

### Phase 6: Migration and Testing Strategy

#### 6.1 Backward Compatibility
- All existing Batayeq functionality remains unchanged
- Default chatType ensures existing clients work without modification
- New fitness features are additive, not replacing

#### 6.2 Testing Approach
1. **Isolation Testing**: Test fitness features separately
2. **Integration Testing**: Ensure Batayeq functionality remains intact
3. **Load Testing**: Verify performance with both chat types active

#### 6.3 Deployment Strategy
1. Deploy enhanced socket server
2. Update fitness clients to use new chatType
3. Batayeq clients continue working without changes
4. Monitor both systems independently

### Phase 7: Code Organization

#### 7.1 Method Separation
```javascript
class SocketHelper {
  // Existing methods remain unchanged
  
  // Add fitness-specific helper methods
  private handleFitnessMessage(socket, data) {
    // Fitness message logic
  }
  
  private handleBatayeqMessage(socket, data) {
    // Existing Batayeq logic moved here
  }
  
  // Enhanced main handlers
  private handleMessage(socket, data) {
    if (socket.chatType === 'fitness') {
      return this.handleFitnessMessage(socket, data);
    } else {
      return this.handleBatayeqMessage(socket, data);
    }
  }
}
```

