
import config from '../config/appConfig'
export default class AppHelper {

  static saveDataToCsv: any;

  public checkDomain = (domain: string) => {
    if (domain === "batayeq.com") {
      return true;
    }
    return false;
  }

  public checkTestingDomain = (domain: string) => {
    console.log(domain)
    if (domain === "authentication:5000" || domain === "localhost:3000") {
      return true;
    }
    return false;
  }


  public randomString = (length: number) => {
    let result = '';
    let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }


  public getDocumentCount = async (query: any) => {
    return new Promise<number>(resolve => {
      let count = 0;
      const stream = query.stream();
      stream.on("data", () => ++count);
      stream.on("end", () => resolve(count));
    });
  }
 

  
 
}