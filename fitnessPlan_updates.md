
```

### Phase 6: Migration and Testing Strategy

#### 8.1 Modified Disconnect Event
```javascript
socket.on('disconnect', () => {
  console.log('user disconnected with userid:', userId, 'from socketId:', socket.id);
  
  if (socket.chatType === 'fitness') {
    // Update fitness last seen in user collection
    this.messageDal.updateFitnessLastSeen(userId).then(() => {
      console.log(`Updated fitness last seen for user: ${userId}`);
    }).catch((error) => {
      console.error(`Error updating fitness last seen for user ${userId}:`, error);
    });
    
    // Notify fitness contacts that user is offline
    io.emit('fitnessUserOffline', { userId, timestamp: new Date() });
    
  } else {
    // For Batayeq, last seen is handled in leaveGroup or specific order contexts
    // No automatic last seen update on disconnect for Batayeq
  }
  
  // Common disconnect logic (unchanged)
  delete this.userSocketMap[userId];
  io.emit('getOnlineUsers', Object.keys(this.userSocketMap));
  io.emit('onlineStatus', { userId: userId, status: false });
});
```

#### 8.2 MessageDal Enhancement for Fitness Last Seen
```javascript
// Add to messageDal class
async updateFitnessLastSeen(userId) {
  try {
    const result = await this.userCollection.updateOne(
      { _id: userId },
      { 
        $set: { 
          fitnessLastSeen: new Date(),
          lastActive: new Date() // Optional: general last activity
        } 
      }
    );
    
    return {
      status: true,
      message: 'Fitness last seen updated successfully',
      data: result
    };
  } catch (error) {
    console.error('Error updating fitness last seen:', error);
    return {
      status: false,
      message: 'Failed to update fitness last seen',
      error: error.message
    };
  }
}

// Optional: Get fitness last seen for displaying in chat
async getFitnessLastSeen(userId) {
  try {
    const user = await this.userCollection.findOne(
      { _id: userId },
      { projection: { fitnessLastSeen: 1, name: 1 } }
    );
    
    return {
      status: true,
      data: user
    };
  } catch (error) {
    console.error('Error getting fitness last seen:', error);
    return {
      status: false,
      error: error.message
    };
  }
}
```
