export default {
  domain: process.env.DOMAIN ? process.env.DOMAIN : "",
  app: {
    port: process.env.APP_PORT ? parseInt(process.env.APP_PORT, 10) : 4000,
  },
  dataBase: {
    url: process.env.MONGODB_URL ? process.env.MONGODB_URL : "mongodb://localhost:27017/batayeq"
  },
  services: {
    authentication: {
      baseUrl: process.env.AUTHENTICATION_URL ? process.env.AUTHENTICATION_URL : ""
    },
    application: {
      baseUrl: process.env.APPLICATION_URL ? process.env.APPLICATION_URL : ""
    }

  }
}
