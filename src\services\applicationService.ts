import * as fs from 'fs';
import curlHelper from '../helpers/curlHelper'
// import {FormData} from 'form-data';
import FormData = require("form-data")
import config from '../config/appConfig'

export default class Applicationservice {
  private appBaseUrl = config.services.application.baseUrl;
  private authBaseUrl = config.services.authentication.baseUrl;

  public createUser = async (userId: string, token: string, payload: object): Promise<object> => {

    try {
      const endpoint: string = `${this.appBaseUrl}/memberGoogleRegister`
      let options: object = {
        headers: {
          "content-type": "application/json"
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, payload); // {payload}
      if (!serviceResponseObject.isError) {
        return { status: true, message: "verified successfully", data: serviceResponseObject.body.data }
      }
      console.log("serviceResponseObject", serviceResponseObject);

      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to create user", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("createUser failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }

  public updateUser = async (header: any, memberId: string, payload: object): Promise<object> => {

    try {
      const endpoint: string = `${this.appBaseUrl}/members/${memberId}`
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": header?.authorization ? header.authorization : null,
          "userid": header?.userid ? header.userid : null
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('put', endpoint, options, payload);
      if (!serviceResponseObject.isError) {
        return { status: true, message: "verified successfully", data: serviceResponseObject.body.data }
      }
      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to verify user details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("updateUser failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }

  public deleteUser = async (userId: string, token: string, id: string): Promise<object> => {

    try {
      const endpoint: string = `${this.appBaseUrl}/members/${id}`
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": token ? token : null,
          "userid": userId ? userId : null
        }
      }
      let serviceResponseObject: any = await curlHelper.sendRequest('delete', endpoint, options, {});
      console.log("serviceResponseObject: ", serviceResponseObject)
      if (!serviceResponseObject.isError) {
        return { status: true, message: "Deleted successfully", data: serviceResponseObject.body.data }
      }
      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to verify user details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("updateUser failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }


  // public uploadFile = async (req: any): Promise<object> => {
  //   try {

  //     const endpoint: string = `${this.authBaseUrl}/file`;

  //     const formData: any = new FormData();

  //     if (!req.files || req.files.length === 0) {
  //       return { status: false, message: "No files uploaded" };
  //     }

  //     // Append files to formData with mimetype
  //     req.files.forEach((file: any) => {

  //       formData.append('file', fs.createReadStream(file.path), {
  //         filename: file.originalname,
  //         contentType: file.mimetype
  //       });

  //     });

  //     formData.append('type', req.body.type);

  //     let options: object = {
  //       headers: {
  //         ...formData.getHeaders(),
  //         "authorization": req.headers.authorization ? req.headers.authorization : null,
  //         "userid": req.headers.userid ? req.headers.userid : null
  //       }
  //     };

  //     let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, formData);

  //     if (!serviceResponseObject.isError) {
  //       return { status: true, message: "File uploaded successfully", data: serviceResponseObject.body.data };
  //     }
  //     global.logger.error("failed to upload file", serviceResponseObject);
  //     return { status: false, message: "Failed to upload file", data: serviceResponseObject.body };
  //   } catch (error: any) {
  //     console.log("uploadFile failed with error", error);
  //     return { status: false, message: error.message ? error.message : "Internal server error" };
  //   }
  // }
  public uploadFile = async ( req:any,element: any): Promise<object> => {
    try {

      const endpoint: string = `${this.authBaseUrl}/file`;

      const formData: any = new FormData();

      // Append files to formData with mimetype

        formData.append('file', fs.createReadStream(element.path), {
          filename: element.originalname,
          contentType: element.mimetype
        });


      formData.append('type', req.body.type);

      let options: object = {
        headers: {
          ...formData.getHeaders(),
          "authorization": req.headers.authorization ? req.headers.authorization : null,
          "userid": req.headers.userid ? req.headers.userid : null
        }
      };

      let serviceResponseObject: any = await curlHelper.sendRequest('post', endpoint, options, formData);

      if (!serviceResponseObject.isError) {
        return { status: true, message: "File uploaded successfully", data: serviceResponseObject.body.data };
      }
      global.logger.error("failed to upload file", serviceResponseObject);
      return { status: false, message: "Failed to upload file", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("uploadFile failed with error", error);
      return { status: false, message: error.message ? error.message : "Internal server error" };
    }
  }

  public getFile = async (req: any, id: string): Promise<object> => {

    try {
      const endpoint: string = `${this.authBaseUrl}/file/${id}`
      let options: object = {
        headers: {
          "content-type": "application/json",
          "authorization": req.headers.authorization ? req.headers.authorization : null,
          "userid": req.headers.userid ? req.headers.userid : null
        }
      }

      let serviceResponseObject: any = await curlHelper.sendRequest('get', endpoint, options, {});

      if (!serviceResponseObject.isError) {
        return { status: true, message: "Deleted successfully", data: serviceResponseObject.body.data }
      }
      global.logger.error("failed to verify user", serviceResponseObject)
      return { status: false, message: "failed to verify user details", data: serviceResponseObject.body };
    } catch (error: any) {
      console.log("updateUser failed with error", error)
      return { status: false, message: error.message ? error.message : "Internal server error" }
    }
  }
}
